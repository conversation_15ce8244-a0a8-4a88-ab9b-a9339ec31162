<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    width="800px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="125px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="员工姓名" prop="employeeId">
            <el-select 
              v-model="form.employeeId" 
              placeholder="请选择员工" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in employeeList"
                :key="item.id"
                :label="item.fullName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="所属部门" prop="departmentId">
            <el-select 
              v-model="form.departmentId" 
              placeholder="请选择所属部门" 
              style="width: 100%"
              filterable
              clearable
            >
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="考勤日期" prop="attendanceDate">
            <el-date-picker
              v-model="form.attendanceDate"
              type="date"
              placeholder="选择考勤日期"
              style="width: 100%"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="考勤状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="正常">正常</el-radio>
              <el-radio label="迟到">迟到</el-radio>
              <el-radio label="早退">早退</el-radio>
              <el-radio label="旷工">旷工</el-radio>
              <el-radio label="请假">请假</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="签到时间" prop="checkInTime">
            <el-time-picker
              v-model="form.checkInTime"
              placeholder="选择签到时间"
              style="width: 100%"
              format="HH:mm"
              value-format="HH:mm"
            ></el-time-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="12">
          <el-form-item label="签退时间" prop="checkOutTime">
            <el-time-picker
              v-model="form.checkOutTime"
              placeholder="选择签退时间"
              style="width: 100%"
              format="HH:mm"
              value-format="HH:mm"
            ></el-time-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="纳入统计">
            <el-checkbox v-model="form.includeInStats">纳入统计</el-checkbox>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-form-item label="备注" prop="remarks">
            <el-input type="textarea" v-model="form.remarks" autocomplete="off" :rows="4" placeholder="请输入备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { mapGetters } from 'vuex';
import { isNull } from "@/utils/validate";
import { doInsert, doUpdate } from "@/api/system/attendance/attendanceManagement";

export default {
  name: "AttendanceManagementEdit",
  props: {
    deptList: {
      type: Array,
      default: () => [],
    },
    employeeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      form: {
        tenantId: null,
        dataMonth: null,
        employeeId: null,
        departmentId: null,
        attendanceDate: null,
        checkInTime: null,
        checkOutTime: null,
        status: '正常',
        includeInStats: true,
        remarks: '',
        version: 0
      },
      dict: {},
      rules: {
        employeeId: [
          { required: true, trigger: "change", message: "请选择员工" }
        ],
        departmentId: [
          { required: true, trigger: "change", message: "请选择所属部门" }
        ],
        attendanceDate: [
          { required: true, trigger: "change", message: "请选择考勤日期" }
        ],
        status: [
          { required: true, trigger: "change", message: "请选择考勤状态" }
        ],
        remarks: [
          { max: 2000, message: '备注不能超过2000个字符', trigger: 'blur' }
        ]
      },
      title: "",
      dialogFormVisible: false,
    };
  },
  computed: {
    ...mapGetters({
      tenantId: 'user/tenantId',
      selectedMonth: 'month/selectedMonth',
    }),
  },
  mounted() {
    // 获取字典数据
    this.dict.attendance_status = this.$getDictList("attendance_status");
  },
  methods: {
    // 显示编辑对话框
    async showEdit(row, dataMonth) {
      // 设置表单默认值
      this.form.tenantId = this.tenantId;
      this.form.dataMonth = dataMonth || this.selectedMonth;

      if (!row) {
        // 新增模式
        this.title = "添加考勤记录";
      } else {
        // 编辑模式
        this.title = "编辑考勤记录";
        this.form = Object.assign({}, row);
      }

      this.dialogFormVisible = true;
    },

    // 关闭对话框
    close() {
      this.dialogFormVisible = false;
      this.$refs["form"].resetFields();
      this.form = this.$options.data().form;
    },

    // 保存考勤记录
    save() {
      this.$refs["form"].validate(async (valid) => {
        if (valid) {
          try {
            // 修改
            if (!isNull(this.form.id)) {
              const { msg } = await doUpdate(this.form);
              this.$baseMessage(msg, "success");
            } else {
              const { msg } = await doInsert(this.form);
              this.$baseMessage(msg, "success");
            }

            await this.$emit("fetchData");
            this.close();
          } catch (error) {
            console.error("保存考勤记录失败:", error);
            this.$baseMessage("保存考勤记录失败，请稍后重试", "error");
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>
