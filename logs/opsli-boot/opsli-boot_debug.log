2025-07-19 00:29:37.840 922  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 00:29:37.840 922  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 00:29:38.017 1099 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 00:29:38.018 1100 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 00:29:38.019 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 00:29:38.020 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 00:29:38.020 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 00:29:38.020 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 00:29:38.020 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 00:29:38.021 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 00:29:38.022 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 00:29:38.022 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 00:29:38.022 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 00:29:38.022 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 00:29:38.022 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 00:29:38.023 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 00:29:39.794 2876 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@6859bbd4'
2025-07-19 00:29:39.794 2876 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@4a225014'
2025-07-19 00:29:39.794 2876 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@6bc08a77]}'
2025-07-19 00:29:39.851 2933 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 00:29:39.864 2946 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 00:29:39.873 2955 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 00:29:39.881 2963 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 00:29:39.890 2972 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 00:29:39.899 2981 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 00:29:39.905 2987 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 00:29:39.912 2994 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 00:29:39.920 3002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 00:29:39.928 3010 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 00:29:39.934 3016 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 00:29:39.940 3022 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 00:29:39.947 3029 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 00:29:39.955 3037 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 00:29:39.961 3043 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 00:29:39.968 3050 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 00:29:39.973 3055 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 00:29:39.975 3057 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 00:29:39.982 3064 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 00:29:39.986 3068 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 00:29:39.993 3075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 00:29:39.997 3079 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 00:29:40.002 3084 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 00:29:40.007 3089 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 00:29:40.012 3094 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 00:29:43.687 6769 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 01:08:24.651 1057 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 01:08:24.651 1057 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 01:08:24.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 01:08:24.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 01:08:24.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 01:08:24.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 01:08:24.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 01:08:24.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 01:08:24.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 01:08:24.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 01:08:24.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 01:08:24.843 1249 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 01:08:24.844 1250 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 01:08:24.844 1250 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 01:08:24.844 1250 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 01:08:24.844 1250 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 01:08:24.845 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 01:08:24.845 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 01:08:24.845 1251 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 01:08:28.741 5147 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@3b8a063d'
2025-07-19 01:08:28.741 5147 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@3a36da5e'
2025-07-19 01:08:28.741 5147 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@758ac46]}'
2025-07-19 01:08:28.800 5206 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 01:08:28.813 5219 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 01:08:28.823 5229 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 01:08:28.834 5240 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 01:08:28.842 5248 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 01:08:28.850 5256 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 01:08:28.856 5262 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 01:08:28.864 5270 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 01:08:28.871 5277 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 01:08:28.902 5308 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 01:08:28.963 5369 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 01:08:28.982 5388 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 01:08:28.994 5400 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 01:08:29.002 5408 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 01:08:29.009 5415 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 01:08:29.015 5421 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 01:08:29.021 5427 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 01:08:29.023 5429 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 01:08:29.028 5434 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 01:08:29.034 5440 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 01:08:29.039 5445 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 01:08:29.044 5450 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 01:08:29.050 5456 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 01:08:29.055 5461 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 01:08:29.059 5465 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 01:08:32.791 9197 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 01:12:01.622 921  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 01:12:01.623 922  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 01:12:01.802 1101 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 01:12:01.803 1102 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 01:12:01.804 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 01:12:01.804 1103 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 01:12:01.805 1104 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 01:12:01.806 1105 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 01:12:01.807 1106 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 01:12:01.808 1107 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 01:12:01.808 1107 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 01:12:01.808 1107 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 01:12:03.599 2898 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@7d836c4a'
2025-07-19 01:12:03.599 2898 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@485547ac'
2025-07-19 01:12:03.599 2898 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@73483d4b]}'
2025-07-19 01:12:03.655 2954 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 01:12:03.667 2966 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 01:12:03.678 2977 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 01:12:03.686 2985 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 01:12:03.694 2993 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 01:12:03.703 3002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 01:12:03.709 3008 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 01:12:03.717 3016 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 01:12:03.725 3024 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 01:12:03.731 3030 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 01:12:03.737 3036 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 01:12:03.742 3041 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 01:12:03.750 3049 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 01:12:03.758 3057 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 01:12:03.763 3062 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 01:12:03.769 3068 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 01:12:03.775 3074 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 01:12:03.776 3075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 01:12:03.781 3080 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 01:12:03.786 3085 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 01:12:03.791 3090 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 01:12:03.797 3096 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 01:12:03.801 3100 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 01:12:03.806 3105 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 01:12:03.811 3110 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 01:12:07.534 6833 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 01:32:40.514 1029 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 01:32:40.514 1029 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 01:32:40.753 1268 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 01:32:40.753 1268 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 01:32:40.753 1268 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 01:32:40.753 1268 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 01:32:40.754 1269 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 01:32:40.755 1270 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 01:32:40.757 1272 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 01:32:40.759 1274 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 01:32:40.759 1274 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 01:32:40.760 1275 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 01:32:40.760 1275 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 01:32:40.761 1276 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 01:32:40.761 1276 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 01:32:40.762 1277 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 01:32:40.762 1277 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 01:32:40.763 1278 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 01:32:40.763 1278 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 01:32:40.764 1279 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 01:32:40.765 1280 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 01:32:40.765 1280 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 01:32:40.765 1280 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 01:32:40.765 1280 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 01:32:40.766 1281 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 01:32:40.767 1282 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 01:32:40.767 1282 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 01:32:40.767 1282 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 01:32:44.661 5176 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@3eb8057c'
2025-07-19 01:32:44.661 5176 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@be186df'
2025-07-19 01:32:44.661 5176 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@7c9c7e7d]}'
2025-07-19 01:32:44.722 5237 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 01:32:44.737 5252 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 01:32:44.748 5263 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 01:32:44.757 5272 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 01:32:44.765 5280 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 01:32:44.774 5289 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 01:32:44.780 5295 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 01:32:44.789 5304 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 01:32:44.797 5312 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 01:32:44.805 5320 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 01:32:44.812 5327 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 01:32:44.818 5333 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 01:32:44.824 5339 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 01:32:44.832 5347 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 01:32:44.836 5351 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 01:32:44.844 5359 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 01:32:44.850 5365 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 01:32:44.852 5367 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 01:32:44.857 5372 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 01:32:44.862 5377 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 01:32:44.867 5382 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 01:32:44.871 5386 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 01:32:44.876 5391 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 01:32:44.882 5397 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 01:32:44.886 5401 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 01:32:49.354 9869 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 10:58:22.941 1518 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 10:58:22.941 1518 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 10:58:23.148 1725 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 10:58:23.149 1726 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 10:58:23.150 1727 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 10:58:23.151 1728 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 10:58:23.152 1729 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 10:58:23.152 1729 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 10:58:23.152 1729 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 10:58:23.153 1730 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 10:58:23.153 1730 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 10:58:23.153 1730 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 10:58:23.153 1730 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 10:58:23.153 1730 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 10:58:23.154 1731 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 10:58:23.155 1732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 10:58:23.155 1732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 10:58:23.155 1732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 10:58:23.155 1732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 10:58:23.155 1732 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 10:58:23.156 1733 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 10:58:25.104 3681 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@6ba02f70'
2025-07-19 10:58:25.104 3681 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@2e88ad38'
2025-07-19 10:58:25.104 3681 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@2a7b4bb6]}'
2025-07-19 10:58:25.164 3741 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 10:58:25.178 3755 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 10:58:25.186 3763 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 10:58:25.197 3774 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 10:58:25.204 3781 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 10:58:25.212 3789 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 10:58:25.218 3795 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 10:58:25.226 3803 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 10:58:25.234 3811 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 10:58:25.242 3819 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 10:58:25.249 3826 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 10:58:25.254 3831 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 10:58:25.261 3838 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 10:58:25.270 3847 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 10:58:25.276 3853 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 10:58:25.281 3858 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 10:58:25.287 3864 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 10:58:25.289 3866 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 10:58:25.293 3870 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 10:58:25.300 3877 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 10:58:25.305 3882 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 10:58:25.310 3887 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 10:58:25.315 3892 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 10:58:25.320 3897 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 10:58:25.324 3901 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 10:58:29.087 7664 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 11:05:19.512 909  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 11:05:19.512 909  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 11:05:19.692 1089 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 11:05:19.693 1090 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 11:05:19.694 1091 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 11:05:19.694 1091 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 11:05:19.694 1091 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 11:05:19.694 1091 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 11:05:19.695 1092 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 11:05:19.696 1093 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 11:05:19.697 1094 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 11:05:19.697 1094 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 11:05:19.697 1094 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 11:05:19.697 1094 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 11:05:19.697 1094 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 11:05:23.624 5021 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@2b625e82'
2025-07-19 11:05:23.624 5021 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@22657db1'
2025-07-19 11:05:23.624 5021 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@61cff1e4]}'
2025-07-19 11:05:23.676 5073 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 11:05:23.688 5085 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 11:05:23.697 5094 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 11:05:23.707 5104 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 11:05:23.716 5113 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 11:05:23.723 5120 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 11:05:23.729 5126 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 11:05:23.736 5133 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 11:05:23.744 5141 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 11:05:23.753 5150 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 11:05:23.759 5156 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 11:05:23.765 5162 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 11:05:23.771 5168 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 11:05:23.780 5177 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 11:05:23.785 5182 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 11:05:23.791 5188 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 11:05:23.796 5193 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 11:05:23.797 5194 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 11:05:23.804 5201 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 11:05:23.809 5206 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 11:05:23.813 5210 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 11:05:23.818 5215 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 11:05:23.823 5220 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 11:05:23.828 5225 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 11:05:23.833 5230 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 11:05:27.533 8930 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 11:23:12.904 984  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 11:23:12.905 985  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 11:23:13.086 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 11:23:13.086 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 11:23:13.086 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 11:23:13.086 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 11:23:13.086 1166 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 11:23:13.087 1167 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 11:23:13.088 1168 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 11:23:13.089 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 11:23:13.089 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 11:23:13.089 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 11:23:13.089 1169 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 11:23:13.090 1170 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 11:23:13.091 1171 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 11:23:13.092 1172 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 11:23:13.092 1172 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 11:23:13.095 1175 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 11:23:13.095 1175 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 11:23:13.095 1175 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 11:23:13.096 1176 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 11:23:16.922 5002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@5ce0f50a'
2025-07-19 11:23:16.922 5002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@c3a1d7f'
2025-07-19 11:23:16.922 5002 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@30d944d8]}'
2025-07-19 11:23:16.982 5062 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 11:23:16.995 5075 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 11:23:17.004 5084 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 11:23:17.014 5094 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 11:23:17.022 5102 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 11:23:17.030 5110 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 11:23:17.036 5116 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 11:23:17.043 5123 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 11:23:17.051 5131 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 11:23:17.059 5139 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 11:23:17.066 5146 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 11:23:17.072 5152 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 11:23:17.079 5159 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 11:23:17.087 5167 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 11:23:17.093 5173 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 11:23:17.099 5179 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 11:23:17.105 5185 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 11:23:17.107 5187 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 11:23:17.112 5192 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 11:23:17.120 5200 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 11:23:17.124 5204 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 11:23:17.129 5209 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 11:23:17.135 5215 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 11:23:17.139 5219 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 11:23:17.144 5224 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 11:23:20.891 8971 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 11:39:17.273 903  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 11:39:17.273 903  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 11:39:17.449 1079 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 11:39:17.449 1079 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 11:39:17.450 1080 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 11:39:17.451 1081 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 11:39:17.451 1081 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 11:39:17.451 1081 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 11:39:17.452 1082 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 11:39:17.452 1082 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 11:39:17.452 1082 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 11:39:17.452 1082 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 11:39:17.453 1083 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 11:39:17.454 1084 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 11:39:17.455 1085 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 11:39:17.455 1085 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 11:39:17.455 1085 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 11:39:17.455 1085 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 11:39:17.455 1085 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 11:39:19.160 2790 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@4d529bc0'
2025-07-19 11:39:19.161 2791 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@429dde4d'
2025-07-19 11:39:19.161 2791 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@62b0792]}'
2025-07-19 11:39:19.226 2856 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 11:39:19.241 2871 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 11:39:19.252 2882 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 11:39:19.262 2892 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 11:39:19.271 2901 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 11:39:19.280 2910 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 11:39:19.287 2917 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 11:39:19.294 2924 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 11:39:19.302 2932 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 11:39:19.308 2938 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 11:39:19.315 2945 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 11:39:19.320 2950 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 11:39:19.326 2956 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 11:39:19.335 2965 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 11:39:19.340 2970 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 11:39:19.346 2976 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 11:39:19.352 2982 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 11:39:19.354 2984 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 11:39:19.358 2988 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 11:39:19.364 2994 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 11:39:19.369 2999 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 11:39:19.374 3004 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 11:39:19.380 3010 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 11:39:19.384 3014 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 11:39:19.388 3018 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 11:39:22.888 6518 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 12:09:43.088 977  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 12:09:43.088 977  [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 12:09:43.265 1154 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 12:09:43.266 1155 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 12:09:43.267 1156 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 12:09:43.268 1157 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 12:09:43.269 1158 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 12:09:43.270 1159 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 12:09:43.270 1159 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 12:09:43.270 1159 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 12:09:43.270 1159 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 12:09:43.270 1159 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 12:09:43.271 1160 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 12:09:43.271 1160 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 12:09:43.271 1160 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 12:09:43.271 1160 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 12:09:47.110 4999 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@2b625e82'
2025-07-19 12:09:47.110 4999 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@22657db1'
2025-07-19 12:09:47.110 4999 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@61cff1e4]}'
2025-07-19 12:09:47.170 5059 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 12:09:47.183 5072 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 12:09:47.192 5081 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 12:09:47.200 5089 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 12:09:47.211 5100 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 12:09:47.219 5108 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 12:09:47.225 5114 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 12:09:47.233 5122 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 12:09:47.240 5129 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 12:09:47.247 5136 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 12:09:47.253 5142 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 12:09:47.261 5150 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 12:09:47.266 5155 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 12:09:47.274 5163 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 12:09:47.279 5168 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 12:09:47.285 5174 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 12:09:47.290 5179 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 12:09:47.292 5181 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 12:09:47.299 5188 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 12:09:47.304 5193 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 12:09:47.311 5200 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 12:09:47.316 5205 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 12:09:47.322 5211 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 12:09:47.328 5217 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 12:09:47.332 5221 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 12:09:51.010 8899 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 13:14:55.218 1017 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 13:14:55.218 1017 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 13:14:55.408 1207 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 13:14:55.409 1208 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 13:14:55.410 1209 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 13:14:55.411 1210 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 13:14:55.411 1210 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 13:14:55.411 1210 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 13:14:55.411 1210 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 13:14:55.412 1211 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 13:14:55.412 1211 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 13:14:55.412 1211 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 13:14:55.412 1211 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 13:14:55.413 1212 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 13:14:55.413 1212 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-19 13:14:55.413 1212 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 13:14:55.413 1212 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 13:14:55.413 1212 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 13:14:55.414 1213 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 13:14:55.415 1214 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 13:14:59.320 5119 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@73839f22'
2025-07-19 13:14:59.321 5120 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@1512efe9'
2025-07-19 13:14:59.321 5120 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@703947bd]}'
2025-07-19 13:14:59.380 5179 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 13:14:59.393 5192 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 13:14:59.402 5201 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 13:14:59.413 5212 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 13:14:59.421 5220 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 13:14:59.430 5229 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 13:14:59.437 5236 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 13:14:59.445 5244 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 13:14:59.454 5253 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 13:14:59.463 5262 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 13:14:59.472 5271 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-07-19 13:14:59.478 5277 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 13:14:59.484 5283 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 13:14:59.491 5290 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 13:14:59.498 5297 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 13:14:59.503 5302 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 13:14:59.509 5308 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 13:14:59.515 5314 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 13:14:59.517 5316 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 13:14:59.523 5322 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 13:14:59.528 5327 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 13:14:59.533 5332 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 13:14:59.538 5337 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 13:14:59.542 5341 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 13:14:59.547 5346 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 13:14:59.551 5350 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 13:15:03.249 9048 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 14:19:37.476 1030 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 14:19:37.476 1030 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 14:19:37.671 1225 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 14:19:37.672 1226 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 14:19:37.673 1227 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 14:19:37.674 1228 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 14:19:37.674 1228 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 14:19:37.674 1228 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 14:19:37.675 1229 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 14:19:37.676 1230 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 14:19:37.677 1231 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 14:19:37.678 1232 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 14:19:37.678 1232 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 14:19:37.678 1232 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 14:19:37.678 1232 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
2025-07-19 14:19:41.562 5116 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisAutoFillInterceptor@66cb9a63'
2025-07-19 14:19:41.563 5117 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'org.opsli.core.filters.interceptor.MybatisCryptoInterceptor@256d8f17'
2025-07-19 14:19:41.563 5117 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Registered plugin: 'MybatisPlusInterceptor{interceptors=[com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor@198a0416]}'
2025-07-19 14:19:41.621 5175 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/xml/SysAreaMapper.xml]'
2025-07-19 14:19:41.634 5188 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/xml/DepartmentMapper.xml]'
2025-07-19 14:19:41.643 5197 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictDetailMapper.xml]'
2025-07-19 14:19:41.653 5207 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/xml/DictMapper.xml]'
2025-07-19 14:19:41.663 5217 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/xml/EmployeeMapper.xml]'
2025-07-19 14:19:41.671 5225 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/LogsMapper.xml]'
2025-07-19 14:19:41.677 5231 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/OperationLogMapper.xml]'
2025-07-19 14:19:41.684 5238 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/xml/SysLoginLogsMapper.xml]'
2025-07-19 14:19:41.693 5247 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/xml/MenuMapper.xml]'
2025-07-19 14:19:41.701 5255 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/xml/SysOptionsMapper.xml]'
2025-07-19 14:19:41.710 5264 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/xml/SysOrgMapper.xml]'
2025-07-19 14:19:41.718 5272 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/xml/PositionMapper.xml]'
2025-07-19 14:19:41.726 5280 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMapper.xml]'
2025-07-19 14:19:41.731 5285 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/xml/RoleMenuRefMapper.xml]'
2025-07-19 14:19:41.737 5291 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/xml/TenantMapper.xml]'
2025-07-19 14:19:41.744 5298 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserMapper.xml]'
2025-07-19 14:19:41.748 5302 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserOrgRefMapper.xml]'
2025-07-19 14:19:41.754 5308 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/xml/UserRoleRefMapper.xml]'
2025-07-19 14:19:41.760 5314 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/xml/GenTableColumnMapper.xml]'
2025-07-19 14:19:41.762 5316 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/xml/MySQLDatabaseTableMapper.xml]'
2025-07-19 14:19:41.768 5322 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/xml/GenLogsMapper.xml]'
2025-07-19 14:19:41.773 5327 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/xml/GenTableMapper.xml]'
2025-07-19 14:19:41.778 5332 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateDetailMapper.xml]'
2025-07-19 14:19:41.782 5336 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/xml/GenTemplateMapper.xml]'
2025-07-19 14:19:41.786 5340 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/xml/TestCarMapper.xml]'
2025-07-19 14:19:41.790 5344 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/xml/TestUserMapper.xml]'
2025-07-19 14:19:41.796 5350 [main] DEBUG c.b.m.e.s.MybatisSqlSessionFactoryBean - Parsed mapper file: 'file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/xml/TestMapper.xml]'
2025-07-19 14:19:45.598 9152 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
2025-07-19 15:03:42.634 1040 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Searching for mappers annotated with @Mapper
2025-07-19 15:03:42.634 1040 [main] DEBUG c.b.m.a.MybatisPlusAutoConfiguration - Using auto-configuration base package 'org.opsli'
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/area/mapper/SysAreaMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/department/mapper/DepartmentMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictDetailMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/dict/mapper/DictMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/employee/mapper/EmployeeMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/LogsMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/OperationLogMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/logs/mapper/SysLoginLogsMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/menu/mapper/MenuMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/options/mapper/SysOptionsMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/org/mapper/SysOrgMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/position/mapper/PositionMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMapper.class]
2025-07-19 15:03:42.834 1240 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/role/mapper/RoleMenuRefMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/tenant/mapper/TenantMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserOrgRefMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-system/target/classes/org/opsli/modulars/system/user/mapper/UserRoleRefMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/column/mapper/GenTableColumnMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/general/actuator/SQLActuator.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/importable/mapper/MySQLDatabaseTableMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/logs/mapper/GenLogsMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/table/mapper/GenTableMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateDetailMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-generator/target/classes/org/opsli/modulars/generator/template/mapper/GenTemplateMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/carinfo/mapper/TestCarMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/gentest/user/mapper/TestUserMapper.class]
2025-07-19 15:03:42.835 1241 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Identified candidate component class: file [/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-modulars/opsli-modulars-test/target/classes/org/opsli/modulars/test/mapper/TestMapper.class]
2025-07-19 15:03:42.836 1242 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysAreaMapper' and 'org.opsli.modulars.system.area.mapper.SysAreaMapper' mapperInterface
2025-07-19 15:03:42.837 1243 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'departmentMapper' and 'org.opsli.modulars.system.department.mapper.DepartmentMapper' mapperInterface
2025-07-19 15:03:42.837 1243 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictDetailMapper' and 'org.opsli.modulars.system.dict.mapper.DictDetailMapper' mapperInterface
2025-07-19 15:03:42.837 1243 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'dictMapper' and 'org.opsli.modulars.system.dict.mapper.DictMapper' mapperInterface
2025-07-19 15:03:42.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'employeeMapper' and 'org.opsli.modulars.system.employee.mapper.EmployeeMapper' mapperInterface
2025-07-19 15:03:42.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'logsMapper' and 'org.opsli.modulars.system.logs.mapper.LogsMapper' mapperInterface
2025-07-19 15:03:42.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'operationLogMapper' and 'org.opsli.modulars.system.logs.mapper.OperationLogMapper' mapperInterface
2025-07-19 15:03:42.838 1244 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysLoginLogsMapper' and 'org.opsli.modulars.system.logs.mapper.SysLoginLogsMapper' mapperInterface
2025-07-19 15:03:42.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'menuMapper' and 'org.opsli.modulars.system.menu.mapper.MenuMapper' mapperInterface
2025-07-19 15:03:42.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOptionsMapper' and 'org.opsli.modulars.system.options.mapper.SysOptionsMapper' mapperInterface
2025-07-19 15:03:42.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'sysOrgMapper' and 'org.opsli.modulars.system.org.mapper.SysOrgMapper' mapperInterface
2025-07-19 15:03:42.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'positionMapper' and 'org.opsli.modulars.system.position.mapper.PositionMapper' mapperInterface
2025-07-19 15:03:42.839 1245 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMapper' and 'org.opsli.modulars.system.role.mapper.RoleMapper' mapperInterface
2025-07-19 15:03:42.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'roleMenuRefMapper' and 'org.opsli.modulars.system.role.mapper.RoleMenuRefMapper' mapperInterface
2025-07-19 15:03:42.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'tenantMapper' and 'org.opsli.modulars.system.tenant.mapper.TenantMapper' mapperInterface
2025-07-19 15:03:42.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userMapper' and 'org.opsli.modulars.system.user.mapper.UserMapper' mapperInterface
2025-07-19 15:03:42.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userOrgRefMapper' and 'org.opsli.modulars.system.user.mapper.UserOrgRefMapper' mapperInterface
2025-07-19 15:03:42.840 1246 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'userRoleRefMapper' and 'org.opsli.modulars.system.user.mapper.UserRoleRefMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableColumnMapper' and 'org.opsli.modulars.generator.column.mapper.GenTableColumnMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'SQLActuator' and 'org.opsli.modulars.generator.general.actuator.SQLActuator' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'mySQLDatabaseTableMapper' and 'org.opsli.modulars.generator.importable.mapper.MySQLDatabaseTableMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genLogsMapper' and 'org.opsli.modulars.generator.logs.mapper.GenLogsMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTableMapper' and 'org.opsli.modulars.generator.table.mapper.GenTableMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateDetailMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateDetailMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'genTemplateMapper' and 'org.opsli.modulars.generator.template.mapper.GenTemplateMapper' mapperInterface
2025-07-19 15:03:42.841 1247 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testCarMapper' and 'org.opsli.modulars.gentest.carinfo.mapper.TestCarMapper' mapperInterface
2025-07-19 15:03:42.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testUserMapper' and 'org.opsli.modulars.gentest.user.mapper.TestUserMapper' mapperInterface
2025-07-19 15:03:42.842 1248 [main] DEBUG o.m.s.mapper.ClassPathMapperScanner - Creating MapperFactoryBean with name 'testMapper' and 'org.opsli.modulars.test.mapper.TestMapper' mapperInterface
